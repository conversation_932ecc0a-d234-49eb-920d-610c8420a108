# Chat with DB - AI-Powered MongoDB Chat Interface

A full-stack TypeScript application that allows users to interact with MongoDB databases through natural language using a locally hosted Ollama AI model.

## Features

- 🤖 Natural language queries to MongoDB using LangChain.js and Ollama
- 💬 Clean, responsive chat interface built with React
- 🔍 Automatic database schema inspection
- 📊 Query result visualization
- 🚀 Single command startup for both frontend and backend
- 🔧 TypeScript throughout the stack

## Prerequisites

Before running this application, ensure you have:

1. **Node.js** (v18 or higher)
2. **MongoDB** running locally on default port (27017)
3. **Ollama** installed and running locally with a model (e.g., llama2)

### Installing Ollama

1. Install Ollama from [https://ollama.ai](https://ollama.ai)
2. Pull a model: `ollama pull llama2`
3. Verify it's running: `ollama list`

## Quick Start

1. **Clone and install dependencies:**
   ```bash
   git clone <repository-url>
   cd chatWithDb
   npm install
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration if needed
   ```

3. **Start the application:**
   ```bash
   npm run dev
   ```

   This will start both the backend (port 3001) and frontend (port 3000) concurrently.

4. **Open your browser:**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Project Structure

```
chatWithDb/
├── backend/                 # Express.js API server
│   ├── src/
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   └── types/          # TypeScript definitions
│   └── package.json
├── frontend/               # React application
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── services/       # API communication
│   │   └── types/          # TypeScript definitions
│   └── package.json
└── package.json           # Root workspace configuration
```

## API Endpoints

- `POST /api/chat` - Send a natural language query
- `GET /api/health` - Check service status
- `GET /api/collections` - List available collections

## Configuration

### Environment Variables

- `MONGODB_URI` - MongoDB connection string (default: mongodb://localhost:27017)
- `OLLAMA_MODEL` - Ollama model name (default: llama2)
- `PORT` - Backend server port (default: 3001)

### Default Database Settings

The application currently connects to:
- Database: `test`
- Collection: `users`

You can modify these in the chat API endpoint or extend the UI to allow selection.

## Example Queries

Try asking questions like:
- "How many users do we have?"
- "Show me users created this month"
- "What are the most common user ages?"
- "Find users with email containing 'gmail'"

## Development

### Running Individual Services

```bash
# Backend only
npm run dev:backend

# Frontend only
npm run dev:frontend
```

### Building for Production

```bash
npm run build
```

## Troubleshooting

1. **Ollama Connection Issues:**
   - Ensure Ollama is running: `ollama serve`
   - Check if your model is available: `ollama list`

2. **MongoDB Connection Issues:**
   - Verify MongoDB is running: `mongosh`
   - Check connection string in `.env`

3. **Port Conflicts:**
   - Backend runs on port 3001
   - Frontend runs on port 3000
   - Modify ports in respective package.json files if needed

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details
