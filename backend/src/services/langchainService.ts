import { ChatOllama } from '@langchain/community/chat_models/ollama';
import { PromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';
import { StringOutputParser } from '@langchain/core/output_parsers';
import { MongoService } from './mongoService';

export class LangChainService {
  private llm: ChatOllama;
  private mongoService: MongoService;

  constructor(mongoService: MongoService, modelName: string = 'llama2') {
    this.mongoService = mongoService;
    this.llm = new ChatOllama({
      baseUrl: 'http://localhost:11434',
      model: modelName,
      temperature: 0.1,
    });
  }

  private createQueryGenerationPrompt(): PromptTemplate {
    return PromptTemplate.fromTemplate(`
You are a MongoDB query expert. Given a user question and database schema, generate a MongoDB query.

Database Schema:
Collection: {collectionName}
Sample Document: {sampleDocument}
Available Fields: {fields}

User Question: {question}

Generate a MongoDB query object that answers the user's question. Return ONLY a valid JSON object that can be used with MongoDB's find() method.
If the query requires aggregation, return an object with an "aggregate" property containing the pipeline array.
If it's a simple find query, return an object with a "find" property containing the filter.

Examples:
- For "find all users": {{"find": {{}}}}
- For "count users by age": {{"aggregate": [{{"$group": {{"_id": "$age", "count": {{"$sum": 1}}}}}}]}}
- For "find users named John": {{"find": {{"name": "John"}}}}

Query:
`);
  }

  private createResponseGenerationPrompt(): PromptTemplate {
    return PromptTemplate.fromTemplate(`
You are a helpful assistant that explains database query results in natural language.

User Question: {question}
MongoDB Query: {query}
Query Results: {results}

Provide a clear, conversational response that:
1. Answers the user's question based on the results
2. Mentions key insights from the data
3. Is easy to understand for non-technical users

If there are no results, explain that no matching data was found.
If there's an error, explain what went wrong in simple terms.

Response:
`);
  }

  async processUserQuestion(
    question: string, 
    collectionName: string, 
    databaseName: string = 'test'
  ): Promise<{ response: string; query?: any; results?: any[] }> {
    try {
      // Connect to database and get schema
      await this.mongoService.connect(databaseName);
      const schema = await this.mongoService.getCollectionSchema(collectionName);

      // Generate MongoDB query
      const queryPrompt = this.createQueryGenerationPrompt();
      const queryChain = RunnableSequence.from([
        queryPrompt,
        this.llm,
        new StringOutputParser(),
      ]);

      const queryResult = await queryChain.invoke({
        question,
        collectionName,
        sampleDocument: JSON.stringify(schema.sampleDocument, null, 2),
        fields: schema.fields.join(', '),
      });

      // Parse the generated query
      let mongoQuery;
      try {
        // Clean up the response to extract JSON
        const cleanedQuery = queryResult.trim().replace(/```json\n?|\n?```/g, '');
        mongoQuery = JSON.parse(cleanedQuery);
      } catch (parseError) {
        console.error('Failed to parse generated query:', queryResult);
        throw new Error('Failed to generate valid MongoDB query');
      }

      // Execute the query
      const results = await this.mongoService.executeQuery(collectionName, mongoQuery);

      // Generate natural language response
      const responsePrompt = this.createResponseGenerationPrompt();
      const responseChain = RunnableSequence.from([
        responsePrompt,
        this.llm,
        new StringOutputParser(),
      ]);

      const naturalResponse = await responseChain.invoke({
        question,
        query: JSON.stringify(mongoQuery, null, 2),
        results: JSON.stringify(results.slice(0, 5), null, 2), // Limit results for context
      });

      return {
        response: naturalResponse,
        query: mongoQuery,
        results: results,
      };

    } catch (error) {
      console.error('Error processing user question:', error);
      return {
        response: `I'm sorry, I encountered an error while processing your question: ${error instanceof Error ? error.message : 'Unknown error'}`,
        query: undefined,
        results: undefined,
      };
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      const response = await this.llm.invoke('Hello, are you working?');
      console.log('Ollama connection test successful');
      return true;
    } catch (error) {
      console.error('Ollama connection test failed:', error);
      return false;
    }
  }
}
